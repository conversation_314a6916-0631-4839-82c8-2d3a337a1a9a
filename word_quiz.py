#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单词学习检测程序
使用rich库创建交互式单词测试
"""

import random
import csv
import os
from typing import Dict, List, Tuple
from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich import print as rprint

class WordQuiz:
    def __init__(self, csv_file="ic_vocabulary.csv"):
        self.console = Console()
        self.csv_file = csv_file
        self.word_bank = {}
        self.load_vocabulary_from_csv()

        # 学习进度跟踪
        self.word_progress = {}  # {word: {"correct_count": 0, "total_attempts": 0}}
        self.mastered_words = set()
        self.current_quiz_words = []

    def load_vocabulary_from_csv(self):
        """从CSV文件加载词汇"""
        try:
            if not os.path.exists(self.csv_file):
                rprint(f"[red]错误：找不到词汇文件 {self.csv_file}[/red]")
                rprint("[yellow]使用默认词汇库...[/yellow]")
                self.load_default_vocabulary()
                return

            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    word = row['word'].strip()
                    definition = row['definition'].strip()
                    wrong_options = [
                        row['wrong_option1'].strip(),
                        row['wrong_option2'].strip(),
                        row['wrong_option3'].strip()
                    ]

                    self.word_bank[word] = {
                        "definition": definition,
                        "wrong_options": wrong_options
                    }

            rprint(f"[green]成功加载 {len(self.word_bank)} 个单词[/green]")

        except Exception as e:
            rprint(f"[red]加载词汇文件时出错：{e}[/red]")
            rprint("[yellow]使用默认词汇库...[/yellow]")
            self.load_default_vocabulary()

    def load_default_vocabulary(self):
        """加载默认词汇库"""
        self.word_bank = {
            "transistor": {
                "definition": "晶体管，一种半导体器件",
                "wrong_options": ["电阻器", "电容器", "二极管"]
            },
            "semiconductor": {
                "definition": "半导体，导电性介于导体和绝缘体之间的材料",
                "wrong_options": ["导体", "绝缘体", "超导体"]
            },
            "amplifier": {
                "definition": "放大器，用于增强信号强度的电路",
                "wrong_options": ["滤波器", "振荡器", "调制器"]
            },
            "capacitor": {
                "definition": "电容器，储存电荷的电子元件",
                "wrong_options": ["电感器", "变压器", "继电器"]
            },
            "resistor": {
                "definition": "电阻器，限制电流流动的元件",
                "wrong_options": ["电容器", "晶体管", "二极管"]
            }
        }
        
    def initialize_progress(self):
        """初始化学习进度"""
        for word in self.word_bank:
            self.word_progress[word] = {"correct_count": 0, "total_attempts": 0}
    
    def select_quiz_words(self, count=10):
        """选择测试单词"""
        # 优先选择未掌握的单词
        unmastered_words = [word for word in self.word_bank if word not in self.mastered_words]
        
        if len(unmastered_words) >= count:
            self.current_quiz_words = random.sample(unmastered_words, count)
        else:
            # 如果未掌握的单词不够，添加一些已掌握的单词进行复习
            self.current_quiz_words = unmastered_words + random.sample(
                list(self.mastered_words), 
                min(count - len(unmastered_words), len(self.mastered_words))
            )
        
        random.shuffle(self.current_quiz_words)
    
    def create_question(self, word: str) -> Tuple[str, List[str], int]:
        """创建单词问题"""
        correct_answer = self.word_bank[word]["definition"]
        wrong_options = self.word_bank[word]["wrong_options"]
        
        # 创建选项列表
        options = [correct_answer] + wrong_options
        random.shuffle(options)
        
        correct_index = options.index(correct_answer)
        
        return word, options, correct_index
    
    def display_question(self, word: str, options: List[str], question_num: int, total_questions: int):
        """显示问题"""
        self.console.clear()
        
        # 显示进度
        progress_text = f"问题 {question_num}/{total_questions}"
        rprint(f"[bold blue]{progress_text}[/bold blue]")
        rprint()
        
        # 显示单词
        word_panel = Panel(
            Text(word, style="bold yellow", justify="center"),
            title="单词",
            border_style="blue"
        )
        rprint(word_panel)
        rprint()
        
        # 显示选项
        rprint("[bold green]请选择正确的释义：[/bold green]")
        for i, option in enumerate(options, 1):
            rprint(f"[cyan]{i}.[/cyan] {option}")
        
        rprint(f"[red]{len(options) + 1}.[/red] 不会")
        rprint()
    
    def get_user_choice(self, num_options: int) -> int:
        """获取用户选择"""
        while True:
            try:
                choice = Prompt.ask(
                    "请输入选项编号",
                    choices=[str(i) for i in range(1, num_options + 2)]
                )
                return int(choice)
            except (ValueError, KeyboardInterrupt):
                rprint("[red]请输入有效的选项编号[/red]")
    
    def update_progress(self, word: str, is_correct: bool):
        """更新学习进度"""
        self.word_progress[word]["total_attempts"] += 1
        
        if is_correct:
            self.word_progress[word]["correct_count"] += 1
            
            # 检查是否掌握（连续两次正确）
            if self.word_progress[word]["correct_count"] >= 2:
                self.mastered_words.add(word)
        else:
            # 如果答错，重置正确计数
            self.word_progress[word]["correct_count"] = 0
            if word in self.mastered_words:
                self.mastered_words.remove(word)
    
    def show_result(self, word: str, is_correct: bool, correct_answer: str):
        """显示答题结果"""
        if is_correct:
            rprint(f"[bold green]✓ 正确！[/bold green]")
        else:
            rprint(f"[bold red]✗ 错误[/bold red]")
            rprint(f"[yellow]正确答案：{correct_answer}[/yellow]")
        
        # 显示进度
        progress = self.word_progress[word]
        status = "已掌握" if word in self.mastered_words else f"正确 {progress['correct_count']}/2 次"
        rprint(f"[blue]进度：{status}[/blue]")
        rprint()
        
        Prompt.ask("按回车继续", default="")
    
    def show_final_summary(self):
        """显示最终总结"""
        self.console.clear()
        
        rprint("[bold blue]测试完成！[/bold blue]")
        rprint()
        
        # 创建结果表格
        table = Table(title="学习进度总结")
        table.add_column("单词", style="cyan")
        table.add_column("状态", style="green")
        table.add_column("正确次数", style="yellow")
        table.add_column("总尝试次数", style="blue")
        
        for word in self.current_quiz_words:
            progress = self.word_progress[word]
            status = "✓ 已掌握" if word in self.mastered_words else "需要继续练习"
            table.add_row(
                word,
                status,
                str(progress["correct_count"]),
                str(progress["total_attempts"])
            )
        
        rprint(table)
        rprint()
        
        mastered_count = len([w for w in self.current_quiz_words if w in self.mastered_words])
        rprint(f"[bold green]已掌握单词：{mastered_count}/{len(self.current_quiz_words)}[/bold green]")
    
    def run_quiz(self):
        """运行测试"""
        self.console.clear()
        rprint("[bold blue]欢迎使用单词学习检测程序！[/bold blue]")
        rprint()
        
        self.initialize_progress()
        self.select_quiz_words(10)
        
        for i, word in enumerate(self.current_quiz_words, 1):
            word_text, options, correct_index = self.create_question(word)
            
            self.display_question(word_text, options, i, len(self.current_quiz_words))
            
            user_choice = self.get_user_choice(len(options))
            
            if user_choice == len(options) + 1:  # 选择"不会"
                is_correct = False
                self.update_progress(word, False)
                self.show_result(word, False, options[correct_index])
            else:
                is_correct = (user_choice - 1) == correct_index
                self.update_progress(word, is_correct)
                self.show_result(word, is_correct, options[correct_index])
        
        self.show_final_summary()

def main():
    """主函数"""
    quiz = WordQuiz()
    
    while True:
        try:
            quiz.run_quiz()
            
            rprint()
            continue_quiz = Prompt.ask(
                "是否继续测试？",
                choices=["y", "n"],
                default="n"
            )
            
            if continue_quiz.lower() != "y":
                break
                
        except KeyboardInterrupt:
            rprint("\n[yellow]程序已退出[/yellow]")
            break
    
    rprint("[bold blue]感谢使用单词学习检测程序！[/bold blue]")

if __name__ == "__main__":
    main()
