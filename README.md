# 单词学习检测程序

一个使用Python和rich库开发的交互式单词学习检测程序，专门用于检测集成电路专业英语词汇的掌握情况。

## 功能特点

- 🎯 **智能测试**：每次提供10个单词进行测试
- 🔄 **重复学习**：每个单词至少展示两次，两次都正确才算掌握
- 🎲 **随机展示**：单词随机出现，避免记忆顺序
- ❌ **诚实选项**：支持"不会"选项，鼓励诚实回答
- 📊 **进度跟踪**：实时显示学习进度和掌握情况
- 🎨 **美观界面**：使用rich库创建美观的终端界面

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装rich库：

```bash
pip install rich
```

## 使用方法

运行程序：

```bash
python word_quiz.py
```

### 操作说明

1. 程序启动后会随机选择10个单词进行测试
2. 对于每个单词，选择正确的释义（输入选项编号）
3. 如果不知道答案，可以选择"不会"选项
4. 程序会跟踪每个单词的学习进度
5. 每个单词需要连续答对2次才算掌握
6. 测试结束后会显示详细的学习进度总结

### 学习规则

- ✅ **掌握条件**：连续答对2次
- 🔄 **重置机制**：答错一次会重置该单词的正确计数
- 📈 **优先级**：优先测试未掌握的单词
- 🔁 **循环测试**：可以重复进行测试直到掌握所有单词

## 词汇库

程序内置了10个集成电路相关的专业英语词汇：

- transistor (晶体管)
- semiconductor (半导体)
- amplifier (放大器)
- capacitor (电容器)
- resistor (电阻器)
- diode (二极管)
- oscillator (振荡器)
- multiplexer (多路复用器)
- inverter (反相器)
- flip-flop (触发器)

## 自定义词汇

如果需要添加更多词汇，可以修改 `word_quiz.py` 文件中的 `word_bank` 字典：

```python
self.word_bank = {
    "新单词": {
        "definition": "正确释义",
        "wrong_options": ["错误选项1", "错误选项2", "错误选项3"]
    },
    # 添加更多单词...
}
```

## 系统要求

- Python 3.6+
- rich库 13.0.0+

## 特性展示

- 🎨 彩色终端界面
- 📊 实时进度显示
- 🎯 智能单词选择算法
- 📈 详细的学习统计
- 🔄 可重复测试

## 贡献

欢迎提交Issue和Pull Request来改进这个程序！
